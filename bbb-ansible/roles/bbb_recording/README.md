# BBB Recording and Processing Architecture

## Overview

This implementation provides immediate video processing for BigBlueButton recordings through a distributed architecture that separates recording and processing responsibilities across dedicated servers.

**Architecture Flow:**
```
Meeting End → Recording Server → Raw File Transfer → Processing Server → Video Processing → S3 Upload → Cleanup
```

**Key Benefits:**
- **Immediate Processing**: Videos processed as soon as meetings end (not daily batches)
- **Resource Efficiency**: Recording servers focus only on recording
- **Centralized Processing**: All video generation happens on dedicated processing server
- **Scalability**: Multiple recording servers → Single processing server

---

## Recording Servers

Recording servers capture BigBlueButton meetings and immediately transfer raw files to the processing server for video generation.

### Workflow

1. **Meeting Ends** → B<PERSON> calls post-archive hooks
2. **Raw Archive** → Creates tar.gz of raw recording files
3. **SSH Transfer** → Securely transfers to processing server
4. **Verification** → Confirms successful transfer
5. **Cleanup** → Removes temporary files

### Files Created by bbb_recording Role

**Post-Archive Hook:**
- `/usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb`
  - Main hook called by BBB after meeting archival
  - Validates meeting ID and calls transfer script
  - Logs all operations to `/var/log/bigbluebutton/post_archive.log`

**Transfer Script:**
- `/usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb`
  - Creates tar.gz archive of raw recording files
  - Transfers via SSH using sshpass authentication
  - Verifies successful transfer on remote server
  - Logs to `/var/log/bigbluebutton/raw_transfer.log`

### Configuration Changes

**Local Processing Disabled:**
```bash
bbb-record --disable video
bbb-record --disable presentation
```

**Workers Stopped:**
```bash
systemctl stop bbb-rap-resque-worker
systemctl stop bbb-record-core
```

**Recording Enabled:**
```bash
bbb-record --enable
```

### Transfer Mechanism

**Authentication:** SSH with sshpass (password-based)
**Archive Format:** tar.gz compression of raw files
**Transfer Path:** `/var/bigbluebutton/processing/` on processing server
**Verification:** Remote file existence check via SSH

**Configuration Variables:**
```yaml
video_processing_server:
  enabled: true
  ip: "************"
  user: "root"
  password: "Aep4qR3bs#Bo7pgf"
  transfer_path: "/var/bigbluebutton/processing"
  trigger_processing: true
```

---

## Processing Server

The processing server receives raw files, processes them into videos, and uploads to DigitalOcean Spaces with immediate processing capability.

### Complete Workflow

1. **File Detection** → `raw_processor.rb` daemon monitors `/var/bigbluebutton/processing/`
2. **Extraction** → Extracts tar.gz to `/var/bigbluebutton/recording/raw/`
3. **BBB Processing** → Triggers `bbb-record --rebuild` for video generation
4. **Video Upload** → `upload_video.rb` uploads to S3 with clean path structure
5. **Cleanup** → Removes local files after successful upload

### Files and Directories

**Core Daemon:**
- `/usr/local/bin/raw_processor.rb`
  - Monitors for incoming tar.gz files
  - Thread-safe processing queue
  - Automatic extraction and BBB processing trigger
  - Logs to `/var/log/bigbluebutton/raw_processor.log`

**Management Scripts:**
- `/usr/local/bin/processing_status.rb`
  - Real-time status monitoring
  - Disk usage and file counts
  - Daemon health checks

- `/usr/local/bin/trigger_processing.rb`
  - Manual processing trigger for specific meeting IDs
  - Useful for reprocessing failed recordings

**Upload Script:**
- `/usr/local/bigbluebutton/core/scripts/post_publish/upload_video.rb`
  - Called automatically by BBB after video processing
  - Uploads to DigitalOcean Spaces with metadata extraction
  - Supports multiple video files per meeting
  - Integrates with Hasura API for calendar events

**Systemd Service:**
- `/etc/systemd/system/raw-processor.service`
  - Auto-restart capability
  - Proper logging and resource limits
  - Runs as `bigbluebutton` user

### Directory Structure

```
/var/bigbluebutton/
├── processing/              # Incoming tar.gz files (monitored)
├── recording/raw/           # Extracted raw files
├── published/video/         # Final processed videos (before upload)
└── ...

/var/log/bigbluebutton/
├── raw_processor.log        # Processing daemon logs
├── post_publish.log         # Upload script logs
└── ...

/home/<USER>/
└── .s3cfg                   # DigitalOcean Spaces configuration
```

### S3 Upload Configuration

**Bucket:** `bbb-terraform`
**Path Structure:** `recordings/geerd/YYYY-MM-DD/meeting-id/filename.mp4`
**Access:** Public ACL for direct video access

**S3cmd Configuration:**
```ini
[default]
access_key = DO801UPFGFY2WBYU7FYD
secret_key = dS5dqoaxYTFRAO6j2rQRlKe8x9YHxdpAoOh9mWm+FFc
host_base = fra1.digitaloceanspaces.com
bucket_location = fra1
use_https = True
```

### BBB Workflow Configuration

**File:** `/usr/local/bigbluebutton/core/scripts/bigbluebutton.yml`
```yaml
steps:
  archive: 'sanity'
  sanity: 'captions'
  captions:
    - 'process:presentation'
    - 'process:video'
  'process:presentation': 'publish:presentation'
  'process:video': 'publish:video'
```

**Package Required:** `bbb-playback-video`
**Presentation Disabled:** `bbb-record --disable presentation`

### Monitoring and Management

**Service Management:**
```bash
systemctl status raw-processor.service
systemctl restart raw-processor.service
journalctl -u raw-processor.service -f
```

**Status Monitoring:**
```bash
processing_status.rb                    # Human-readable status
processing_status.rb --json             # JSON output
```

**Manual Processing:**
```bash
trigger_processing.rb <meeting-id>      # Reprocess specific meeting
```

**Log Monitoring:**
```bash
tail -f /var/log/bigbluebutton/raw_processor.log    # Processing daemon
tail -f /var/log/bigbluebutton/post_publish.log     # Upload operations
```

### Troubleshooting

**Service Issues:**
```bash
systemctl status raw-processor.service
journalctl -u raw-processor.service -n 50
```

**Processing Issues:**
```bash
ls -la /var/bigbluebutton/processing/    # Check pending files
ls -la /var/bigbluebutton/recording/raw/ # Check extracted files
bbb-record --list                       # Check BBB recording status
```

**Upload Issues:**
```bash
sudo -u bigbluebutton s3cmd -c /home/<USER>/.s3cfg ls s3://bbb-terraform/
tail -f /var/log/bigbluebutton/post_publish.log
```

**File Permissions:**
```bash
chown -R bigbluebutton:bigbluebutton /var/bigbluebutton/
chown -R bigbluebutton:bigbluebutton /var/log/bigbluebutton/
```

This architecture ensures immediate video processing with reliable file transfer, centralized processing, and automatic cloud storage integration.
